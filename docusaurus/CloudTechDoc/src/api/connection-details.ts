export type ConnectionDetails = {
  serverUrl: string;
  roomName: string;
  participantName: string;
  participantToken: string;
};

export async function GET() {
  try {
    // For development purposes, we'll use mock data
    // In production, you would implement proper LiveKit server integration
    const participantIdentity = `voice_assistant_user_${Math.floor(Math.random() * 10_000)}`;
    const roomName = `voice_assistant_room_${Math.floor(Math.random() * 10_000)}`;

    // Mock connection details - replace with actual LiveKit server configuration
    const data: ConnectionDetails = {
      serverUrl: process.env.LIVEKIT_URL || "wss://your-livekit-server.com",
      roomName,
      participantToken: "mock-token", // In production, generate actual token
      participantName: participantIdentity,
    };

    const headers = new Headers({
      "Cache-Control": "no-store",
    });

    return new Response(JSON.stringify(data), {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-store'
      }
    });
  } catch (error) {
    console.error("Error generating connection details:", error);
    return new Response(JSON.stringify({ error: "Failed to generate connection details" }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
